const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const http = require('http');

let win;

function createWindow() {
  win = new BrowserWindow({
    show: false, // la fenêtre est initialement cachée
    width: 400,
    height: 340,
    resizable: false,
    fullscreenable: false,
    webPreferences: {
      preload: path.join(__dirname, 'renderer.js')
    }
  });

  win.loadFile('index.html');

  // Empêche la fermeture réelle de la fenêtre
  win.on('close', (e) => {
    e.preventDefault();  // empêche Electron de détruire la fenêtre
    win.hide();          // cache la fenêtre au lieu de la fermer
  });
}

ipcMain.on('hide-window', () => {
  if (win && !win.isDestroyed()) {
    win.hide();
  }
});

// HTTP local mini serveur pour déclencher la fenêtre
http.createServer((req, res) => {
  if (req.method === 'POST' && req.url === '/open') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const { title, subtitle } = JSON.parse(body);

      if (win && !win.isDestroyed()) {
        // Envoie les données au renderer
        win.webContents.send('open-form', { title, subtitle });

        // Affiche la fenêtre, en haut et avec le focus
        win.setAlwaysOnTop(true);
        win.show();
        win.focus();
        win.setAlwaysOnTop(false); // facultatif : remet comportement par défaut
      }

      res.end('ok');
    });
  } else {
    res.statusCode = 404;
    res.end('Not Found');
  }
}).listen(3210, () => {
  console.log('🟢 Electron écoute sur http://localhost:3210');
});

// Crée la fenêtre quand Electron est prêt
app.whenReady().then(createWindow);

// Empêche Electron de quitter quand toutes les fenêtres sont fermées (comportement Windows)
app.on('window-all-closed', () => {
  // Ne rien faire — on garde l'app active même sans fenêtre
});

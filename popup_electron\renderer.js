const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  onData: (cb) => ipcRenderer.on('open-form', (_, data) => cb(data)),
  sendData: (data) => {
    fetch('http://localhost:3000/lowerthird/confirm', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).then(() => ipcRenderer.send('hide-window'));
  }
});

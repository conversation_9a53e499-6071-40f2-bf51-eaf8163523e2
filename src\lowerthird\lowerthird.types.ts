/**
 * Types pour le module Lower Third
 */

export interface LowerThirdData {
    title: string;
    subtitle: string;
}

export interface LowerThirdStatusResponse {
    success: boolean;
    data: {
        title: string;
        subtitle: string;
        visible: boolean;
    };
}

export interface LowerThirdConfig {
    service: {
        host: string;
        port: number;
        basePath: string;
    };
    popup: {
        host: string;
        port: number;
        path: string;
    };
}

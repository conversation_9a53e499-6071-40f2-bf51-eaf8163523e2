import { Controller, Get, Post, Body } from '@nestjs/common';
import { HubService } from './core/hub.service';

@Controller()
export class AppController {
    constructor(private readonly hubService: HubService) {}

    @Get('status')
    getStatus() {
        return this.hubService.getStatus();
    }

    @Get('test_lower_third')
    async testLowerThird() {
        const data = { name: '<PERSON>', title: 'Présentateur' };
        await this.openLowerThirdPopup(data);
        return 'test';
    }

    @Post('lowerthird/confirm')
    handleLowerThirdConfirm(@Body() dto: { name: string, title: string }) {
        console.log('Données reçues du popup:', dto);
        // ↪️ ici tu déclenches l'affichage réel, OBS, WebSocket, etc.
        return { status: 'ok' };
    }

    async openLowerThirdPopup(data: { name: string, title: string }) {
        await fetch('http://localhost:3210/open', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: '<PERSON>', title: 'Animateur' }),
        });
    }
}

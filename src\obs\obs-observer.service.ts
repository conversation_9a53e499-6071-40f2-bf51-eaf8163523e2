/**
 * Service d'observation des événements OBS - Version ultra-simplifiée
 * Responsabilité : Écouter OBS, émettre des événements
 */

import { Injectable } from '@nestjs/common';
import OBSWebSocket, { EventSubscription } from 'obs-websocket-js';

export type OBSEventCallback = (type: string, data: any) => void;

export interface AudioSourceConfig {
    inputName: string;
}

@Injectable()
export class OBSObserverService {
    private eventCallback?: OBSEventCallback;

    private readonly HIGH_DB = -40;
    private volumeState: Record<string, 'high' | 'low' | undefined> = {};
    private lastLogTime: Record<string, number> = {};
    private readonly LOG_INTERVAL_MS = 5000; // 5 secondes

    // Debug audio - configuré par les modules qui en ont besoin
    private debugConfig: { enabled: boolean; logThresholds: boolean; intervalMs: number } = {
        enabled: false,
        logThresholds: false,
        intervalMs: 2000,
    };

    // Configuration des sources à écouter - configurée par les modules qui en ont besoin
    private audioSources: AudioSourceConfig[] = [];

    constructor() {}

    setEventCallback(callback: OBSEventCallback): void {
        this.eventCallback = callback;
    }

    /**
     * Configure les sources audio à écouter
     * Appelé par les modules/links qui ont besoin de surveiller des sources audio
     */
    setAudioSources(sources: AudioSourceConfig[]): void {
        this.audioSources = sources;
        console.log(
            `[obs-observer] Configured ${this.audioSources.length} audio sources:`,
            this.audioSources.map((s) => `${s.inputName}`),
        );
    }

    /**
     * Configure les options de debug audio
     */
    setDebugConfig(config: { enabled: boolean; logThresholds: boolean; intervalMs: number }): void {
        this.debugConfig = config;
        if (config.enabled) {
            console.log(`[obs-observer] Debug audio enabled - thresholds: ${config.logThresholds}, interval: ${config.intervalMs}ms`);
        }
    }

    /**
     * Ajoute des sources audio à la liste existante
     */
    addAudioSources(sources: AudioSourceConfig[]): void {
        // Éviter les doublons
        const existingNames = new Set(this.audioSources.map((s) => s.inputName));
        const newSources = sources.filter((s) => !existingNames.has(s.inputName));

        this.audioSources.push(...newSources);

        if (newSources.length > 0) {
            console.log(
                `[obs-observer] Added ${newSources.length} new audio sources:`,
                newSources.map((s) => `${s.inputName}`),
            );
        }
    }

    setupEventListeners(obs: OBSWebSocket): void {
        obs.on('InputMuteStateChanged', (data) => {
            this.handleInputMuteChanged(data.inputName, data.inputMuted);
        });

        obs.on('InputVolumeChanged', (data) => {
            this.handleInputVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
        });

        obs.on('CurrentProgramSceneChanged', (data) => {
            this.handleSceneChanged(data.sceneName);
        });

        obs.on('InputVolumeMeters', ({ inputs }) => {
            inputs.forEach((i) => {
                if (!i.inputName || !i.inputLevelsMul || (i.inputLevelsMul as number[]).length === 0) return;

                // Vérifier si cette source est configurée pour être écoutée
                const sourceConfig = this.audioSources.find((s) => s.inputName === i.inputName);
                if (!sourceConfig) return;

                const rawLinear = i.inputLevelsMul[0];
                const levels = Array.isArray(rawLinear) ? rawLinear : [rawLinear];

                let linear: number;
                if (levels[1]) {
                    linear = (levels[1] as number) || 0;
                } else {
                    linear = (levels[0] as number) || 0;
                }

                this.handleInputMeter(i.inputName as string, linear);
            });
        });
    }

    // === HANDLERS SIMPLES ===

    private handleInputMuteChanged(inputName: string, inputMuted: boolean): void {
        this.emitEvent('input_mute_changed', {
            inputName,
            inputMuted,
        });
    }

    private handleInputVolumeChanged(inputName: string, inputVolumeDb: number, inputVolumeMul: number): void {
        this.emitEvent('input_volume_changed', {
            inputName,
            inputVolumeDb,
            inputVolumeMul,
        });
    }

    private handleSceneChanged(sceneName: string): void {
        this.emitEvent('scene_changed', {
            sceneName,
        });
    }

    private handleInputMeter(inputName: string, linearLvl: number): void {
        const prev = this.volumeState[inputName];

        // Calculs de différentes unités audio
        const audioLevels = this.calculateAudioLevels(linearLvl);

        // Log périodique des niveaux audio (configurable)
        const now = Date.now();
        const logInterval = this.debugConfig.enabled ? this.debugConfig.intervalMs : this.LOG_INTERVAL_MS;

        if (!this.lastLogTime[inputName] || now - this.lastLogTime[inputName] > logInterval) {
            this.lastLogTime[inputName] = now;

            if (this.debugConfig.enabled) {
                // Log détaillé pour le debug
                const thresholdInfo = this.debugConfig.logThresholds ? ` | Seuil: ${this.HIGH_DB}dB | État: ${prev || 'unknown'} → ${audioLevels.dBFS > this.HIGH_DB ? 'HIGH' : 'LOW'}` : '';

                console.log(
                    `[obs-observer] 🎤 ${inputName}: ${audioLevels.dBFS.toFixed(1)}dBFS | ${audioLevels.dBu.toFixed(1)}dBu | ${audioLevels.VU.toFixed(1)}VU | Linear: ${linearLvl.toFixed(6)}${thresholdInfo}`,
                );
            } else {
                // Log standard (désactivé par défaut pour éviter le spam)
                // console.log(`[obs-observer] ${inputName}: ${audioLevels.dBFS.toFixed(1)}dBFS`);
            }
        }

        // Détection des changements d'état (utilise dBFS pour le moment)
        if (audioLevels.dBFS > this.HIGH_DB && prev !== 'high') {
            this.volumeState[inputName] = 'high';

            if (this.debugConfig.enabled && this.debugConfig.logThresholds) {
                console.log(`[obs-observer] 🔊 ${inputName} ACTIVATED: ${audioLevels.dBFS.toFixed(1)}dB > ${this.HIGH_DB}dB`);
            }

            this.emitEvent('input_volume_HIGH', { inputName, dB: audioLevels.dBFS });
        } else if (audioLevels.dBFS <= this.HIGH_DB && prev !== 'low') {
            this.volumeState[inputName] = 'low';

            if (this.debugConfig.enabled && this.debugConfig.logThresholds) {
                console.log(`[obs-observer] 🔇 ${inputName} DEACTIVATED: ${audioLevels.dBFS.toFixed(1)}dB <= ${this.HIGH_DB}dB`);
            }

            this.emitEvent('input_volume_LOW', { inputName, dB: audioLevels.dBFS });
        }
    }

    private calculateAudioLevels(linearLvl: number) {
        // Protection contre les valeurs nulles/négatives
        const safeLinear = Math.max(linearLvl, 1e-8);

        return {
            // dBFS (decibels Full Scale) - Standard numérique
            dBFS: 20 * Math.log10(safeLinear),

            // dBu (decibels unloaded) - Standard professionnel
            // Référence: 0 dBu = 0.775V RMS
            dBu: 20 * Math.log10(safeLinear) + 2.2, // Approximation courante

            // dBV (decibels Volt) - Référence 1V
            dBV: 20 * Math.log10(safeLinear) - 2.2,

            // VU (Volume Unit) - Échelle VU-mètre classique
            // 0 VU ≈ -20 dBFS en numérique
            VU: 20 * Math.log10(safeLinear) + 20,

            // Peak level (crête) - Souvent utilisé dans les DAW
            peak: 20 * Math.log10(safeLinear) + 6,

            // RMS approximation (Root Mean Square)
            RMS: 20 * Math.log10(safeLinear) - 3,
        };
    }

    private emitEvent(type: string, data: any): void {
        if (this.eventCallback) {
            this.eventCallback(type, data);
        }
    }
}

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Lower Third</title>
  <style>
    *, *::before, *::after {
      box-sizing: inherit;
    }

    html, body {
      overflow: hidden; /* supprime scroll même si dépasse */
      height: 100%;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', sans-serif;
      padding: 20px;
      margin: 0;
      background: #f9f9f9;
    }

    h2 {
      text-align: center;
      font-size: 20px;
      margin-bottom: 20px;
    }

    input {
      width: 100%;
      padding: 10px 12px;
      margin-bottom: 15px;
      border: 1px solid #ccc;
      border-radius: 8px;
      box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
      font-size: 14px;
    }

    button {
      width: 100%;
      padding: 10px;
      font-size: 15px;
      background-color: #0078D4;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    button:hover {
      background-color: #005fa3;
    }
  </style>
</head>
<body>
  <h2>Modifier le Lower Third</h2>
  <input type="text" id="title" placeholder="Title">
  <input type="text" id="subtitle" placeholder="Subtitle">
  <button id="submit">Envoyer</button>

  <script>
    window.electronAPI.onData(({ title, subtitle }) => {
        document.getElementById('title').value = title || '';
        document.getElementById('subtitle').value = subtitle || '';
    });

    document.getElementById('submit').addEventListener('click', () => {
        const title = document.getElementById('title').value;
        const subtitle = document.getElementById('subtitle').value;
        window.electronAPI.sendData({ title, subtitle });
    });

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
        document.getElementById('submit').click();
        }
    });
  </script>
</body>
</html>

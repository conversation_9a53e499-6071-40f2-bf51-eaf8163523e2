/**
 * Lien Companion → WinMedia
 * Transmet les commandes personnalisées de Companion vers WinMedia
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, IControlModule } from '../interfaces/module.interface';
import { IWinmediaModule } from '../../winmedia/winmedia.types';

export class CompanionToWinmediaLink implements IModuleLink {
    public readonly name = 'companion-to-winmedia';
    public readonly description = 'Transmet les commandes personnalisées de Companion vers WinMedia';
    public readonly enabled: boolean;

    private companionModule?: IControlModule;
    private winmediaModule?: IWinmediaModule;
    private cleanupCallbacks: Array<() => void> = [];

    constructor(config: LinkConfig) {
        this.enabled = config.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            return;
        }

        // Récupérer les modules nécessaires
        this.companionModule = modules.get('companion') as IControlModule;
        this.winmediaModule = modules.get('winmedia') as IWinmediaModule;

        if (!this.companionModule) {
            console.log(`[${this.name}] Companion module not available - link disabled`);
            return;
        }

        if (!this.winmediaModule) {
            console.log(`[${this.name}] WinMedia module not available - link disabled`);
            return;
        }

        console.log(`[${this.name}] Initializing link between Companion and WinMedia`);

        // S'abonner aux événements de Companion
        this.setupCompanionListeners();

        console.log(`[${this.name}] Link initialized successfully`);
    }

    async cleanup(): Promise<void> {
        console.log(`[${this.name}] Cleaning up link...`);

        // Nettoyer tous les callbacks
        this.cleanupCallbacks.forEach((cleanup) => cleanup());
        this.cleanupCallbacks = [];

        console.log(`[${this.name}] Link cleanup completed`);
    }

    /**
     * Configurer les écouteurs d'événements de Companion
     */
    private setupCompanionListeners(): void {
        if (!this.companionModule || !this.winmediaModule) {
            return;
        }

        // Écouter les actions de Companion
        const companionEventCallback = (event: any) => {
            if (event.type === 'companion_action') {
                this.handleCompanionAction(event.data);
            }
        };

        this.companionModule.onEvent(companionEventCallback);
        this.cleanupCallbacks.push(() => {
            // Note: Dans une implémentation complète, il faudrait pouvoir se désabonner
            // Pour l'instant, on se contente de vider le callback
        });

        console.log(`[${this.name}] Companion event listeners configured`);
    }

    /**
     * Traiter une action reçue de Companion
     */
    private async handleCompanionAction(actionData: any): Promise<void> {
        const { actionType, target } = actionData;

        // Ne traiter que les actions WinMedia custom
        if (actionType === 'winmedia/custom' && target) {
            console.log(`[${this.name}] Processing WinMedia custom command: ${target}`);

            try {
                await this.winmediaModule!.sendCustomCommand(target);
                console.log(`[${this.name}] WinMedia custom command sent successfully: ${target}`);
            } catch (error) {
                console.error(`[${this.name}] Error sending WinMedia command:`, error);
            }
        } else if (actionType === 'winmedia/custom') {
            console.warn(`[${this.name}] WinMedia custom command requires target parameter`);
        }
        // Ignorer silencieusement les autres actions
    }
}

/**
 * Module Winmedia autonome
 * Responsabilité : Envoi de commandes à Winmedia Server via le fichier winmedia.tmp
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import * as fs from 'fs';
import { BaseModule } from '../core/base/base-module';
import { modulesConfig, winmediaConfig } from '../config';
import { IWinmediaModule } from './winmedia.types';

@Injectable()
export class WinmediaModuleService extends BaseModule implements IWinmediaModule, OnModuleInit, OnModuleDestroy {
    private activated: boolean = false;
    private readonly config = winmediaConfig();

    constructor() {
        super('winmedia', modulesConfig().winmedia.enabled);
        this.log('Winmedia module created', { enabled: this.enabled });
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        this.log('Starting Winmedia module...');
        this.activated = true;
        this.updateConnectionStatus({ connected: true });
        this.log('Winmedia module started successfully');
    }

    async stop(): Promise<void> {
        this.log('Stopping Winmedia module...');
        this.activated = false;
        this.updateConnectionStatus({ connected: false });
        this.log('Winmedia module stopped');
    }

    async sendCustomCommand(command: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.activated) {
                this.log('Cannot send command - module is not activated');
                resolve();
                return;
            }

            this.log('Sending command:', command);

            try {
                const filePath = `\\\\${this.config.network.host}\\WinMedia\\winmedia.tmp`;
                fs.appendFileSync(filePath, ' ' + command.trim());
                this.log('Command sent successfully to:', filePath);
                resolve();
            } catch (error) {
                const errorObj = error instanceof Error ? error : new Error(String(error));
                this.handleError(errorObj, 'Failed to send command to WinMedia');
                reject(errorObj);
            }
        });
    }
}

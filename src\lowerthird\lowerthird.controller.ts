import { Controller, Get, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { LowerThirdData, LowerThirdStatusResponse } from './lowerthird.types';
import { lowerThirdConfig } from '../config';

@Controller('lowerthird')
export class LowerThirdController {
    private readonly config = lowerThirdConfig();
    private readonly lowerThirdServiceUrl: string;
    private readonly popupUrl: string;

    constructor() {
        // Forcer l'utilisation d'IPv4 en utilisant 127.0.0.1 au lieu de localhost
        const nodeCGHost = this.config.nodeCG.host === 'localhost' ? '127.0.0.1' : this.config.nodeCG.host;
        const popupHost = this.config.popup.host === 'localhost' ? '127.0.0.1' : this.config.popup.host;

        this.lowerThirdServiceUrl = `http://${nodeCGHost}:${this.config.nodeCG.port}${this.config.nodeCG.basePath}`;
        this.popupUrl = `http://${popupHost}:${this.config.popup.port}${this.config.popup.path}`;
    }

    @Get('popup')
    async popupLowerThird() {
        try {
            // Récupérer les données actuelles depuis le service
            const currentData = await this.getCurrentLowerThirdData();
            
            // Ouvrir la popup avec les données actuelles
            await this.openLowerThirdPopup(currentData);
            
            return { status: 'success', message: 'Lower third popup opened' };
        } catch (error) {
            console.error('Error in testLowerThird:', error);
            throw new HttpException(
                'Failed to open lower third popup',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('confirm')
    async handleLowerThirdConfirm(@Body() dto: LowerThirdData) {
        try {
            console.log('Données reçues du popup:', dto);
            
            // Mettre à jour les données via le service
            await this.updateLowerThirdData(dto);
            
            return { status: 'success', message: 'Lower third updated successfully' };
        } catch (error) {
            console.error('Error updating lower third:', error);
            throw new HttpException(
                'Failed to update lower third data',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Récupérer les données actuelles du service lower third
     */
    private async getCurrentLowerThirdData(): Promise<LowerThirdData> {
        try {
            const response = await fetch(`${this.lowerThirdServiceUrl}/status`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data: LowerThirdStatusResponse = await response.json();

            if (data.success && data.data) {
                return {
                    title: data.data.title || '',
                    subtitle: data.data.subtitle || ''
                };
            } else {
                console.warn('Service returned unsuccessful response:', data);
                return {
                    title: '',
                    subtitle: ''
                };
            }
        } catch (error) {
            console.error('Error fetching current lower third data:', error);
            // Retourner des données par défaut en cas d'erreur
            return {
                title: '',
                subtitle: ''
            };
        }
    }

    /**
     * Mettre à jour les données via le service lower third
     */
    private async updateLowerThirdData(data: LowerThirdData): Promise<void> {
        try {
            const response = await fetch(`${this.lowerThirdServiceUrl}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            console.log('Lower third data updated successfully');
        } catch (error) {
            console.error('Error updating lower third data:', error);
            throw error;
        }
    }

    /**
     * Ouvrir la popup Electron avec les données
     */
    private async openLowerThirdPopup(data: LowerThirdData): Promise<void> {
        try {
            const response = await fetch(this.popupUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`Failed to open popup: ${response.status}`);
            }
            
            console.log('Lower third popup opened successfully');
        } catch (error) {
            console.error('Error opening lower third popup:', error);
            throw error;
        }
    }
}
